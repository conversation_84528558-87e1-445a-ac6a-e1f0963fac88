<?php

namespace App\Http\Controllers;

use App\Models\BillCd;
use App\Models\NoChargeCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CouponController extends Controller
{
    /**
     * Apply coupon/bill code to the order
     */
    public function applyCoupon(Request $request)
    {
        try {
            $couponCode = $request->input('coupon_code');

            if (empty($couponCode)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please enter a coupon code'
                ], 400);
            }

            // First check if it's a Bill Code
            $billCode = BillCd::where('BillCd', $couponCode)->where('CurrInd', 1)->first();
            $compCode = null;
            $discountType = null;

            if ($billCode) {
                $discountType = 'bill';
                if (!$billCode->isValidForDiscount()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'This bill code is not active'
                    ], 400);
                }
            } else {
                // Check if it's a Comp Code
                $compCode = NoChargeCode::where('CompCdId', $couponCode)->where('CurrInd', 1)->first();

                if ($compCode) {
                    $discountType = 'comp';
                    if (!$compCode->isValidForDiscount()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'This comp code is not active'
                        ], 400);
                    }
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid coupon code'
                    ], 404);
                }
            }

            // Get current order data from session
            $orderData = session('order_data', [
                'clubs' => [],
                'order' => ['TtlAmt' => 0]
            ]);

            $currentTotal = $orderData['order']['TtlAmt'] ?? 0;

            if ($currentTotal <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'No items in cart to apply discount'
                ], 400);
            }

            // Calculate discount based on type
            if ($discountType === 'bill') {
                $discountAmount = $billCode->calculateDiscount($currentTotal);
                $discountPercentage = $billCode->getDiscountPercentage();
                $codeType = 'Bill Code';
            } else {
                $discountAmount = $compCode->calculateDiscount($currentTotal);
                $discountPercentage = $compCode->getDiscountPercentage();
                $codeType = 'Comp Code';
            }

            $newTotal = $currentTotal - $discountAmount;

            // Update session with coupon information
            $orderData['coupon'] = [
                'code' => $couponCode,
                'type' => $codeType,
                'discount_amount' => $discountAmount,
                'discount_percentage' => $discountPercentage,
                'original_total' => $currentTotal,
                'new_total' => $newTotal
            ];

            // Update order total and set appropriate field
            $orderData['order']['TtlAmt'] = $newTotal;

            if ($discountType === 'bill') {
                $orderData['order']['BillCd'] = $couponCode;
                unset($orderData['order']['CompCd']); // Remove comp code if exists
            } else {
                $orderData['order']['CompCd'] = $couponCode;
                unset($orderData['order']['BillCd']); // Remove bill code if exists
            }

            session(['order_data' => $orderData]);

            return response()->json([
                'success' => true,
                'message' => $codeType . ' applied successfully! ' . $discountPercentage . '% discount',
                'discount' => $discountPercentage,
                'discount_amount' => $discountAmount,
                'new_total' => $newTotal,
                'original_total' => $currentTotal
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while applying the coupon',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove coupon from the order
     */
    public function removeCoupon(Request $request)
    {
        try {
            $orderData = session('order_data', [
                'clubs' => [],
                'order' => []
            ]);

            if (isset($orderData['coupon'])) {
                // Restore original total
                $orderData['order']['TtlAmt'] = $orderData['coupon']['original_total'];
                unset($orderData['coupon']);
                unset($orderData['order']['BillCd']);
                unset($orderData['order']['CompCd']);

                session(['order_data' => $orderData]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Coupon removed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while removing the coupon'
            ], 500);
        }
    }
}
