<?php

namespace App\Http\Controllers;

use App\Models\BillCd;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CouponController extends Controller
{
    /**
     * Apply coupon/bill code to the order
     */
    public function applyCoupon(Request $request)
    {
        try {
            $couponCode = $request->input('coupon_code');
            
            if (empty($couponCode)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please enter a coupon code'
                ], 400);
            }

            // Find the bill code
            $billCode = BillCd::where('BillCd', $couponCode)->first();
            
            if (!$billCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid coupon code'
                ], 404);
            }

            if (!$billCode->isValidForDiscount()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This coupon code is not active'
                ], 400);
            }

            // Get current order data from session
            $orderData = session('order_data', [
                'clubs' => [],
                'order' => ['TtlAmt' => 0]
            ]);

            $currentTotal = $orderData['order']['TtlAmt'] ?? 0;
            
            if ($currentTotal <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'No items in cart to apply discount'
                ], 400);
            }

            // Calculate discount
            $discountAmount = $billCode->calculateDiscount($currentTotal);
            $discountPercentage = $billCode->getDiscountPercentage($currentTotal);
            $newTotal = $currentTotal - $discountAmount;

            // Update session with coupon information
            $orderData['coupon'] = [
                'code' => $couponCode,
                'discount_amount' => $discountAmount,
                'discount_percentage' => $discountPercentage,
                'original_total' => $currentTotal,
                'new_total' => $newTotal
            ];

            // Update order total
            $orderData['order']['TtlAmt'] = $newTotal;
            $orderData['order']['BillCd'] = $couponCode;

            session(['order_data' => $orderData]);

            return response()->json([
                'success' => true,
                'message' => 'Coupon applied successfully',
                'discount' => $discountPercentage,
                'discount_amount' => $discountAmount,
                'new_total' => $newTotal,
                'original_total' => $currentTotal
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while applying the coupon',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove coupon from the order
     */
    public function removeCoupon(Request $request)
    {
        try {
            $orderData = session('order_data', [
                'clubs' => [],
                'order' => []
            ]);

            if (isset($orderData['coupon'])) {
                // Restore original total
                $orderData['order']['TtlAmt'] = $orderData['coupon']['original_total'];
                unset($orderData['coupon']);
                unset($orderData['order']['BillCd']);
                
                session(['order_data' => $orderData]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Coupon removed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while removing the coupon'
            ], 500);
        }
    }
}
