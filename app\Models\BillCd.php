<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillCd extends Model
{
    use HasFactory , softDeletes;

    protected $table = 'billcd';
    protected $guarded = [];
    protected $primaryKey = 'BillCd';
    protected $keyType = 'string';

    /**
     * Check if bill code is valid for discount
     */
    public function isValidForDiscount()
    {
        // Assuming CurrInd field indicates if the bill code is active
        return isset($this->CurrInd) ? $this->CurrInd == 1 : true;
    }

    /**
     * Calculate discount based on bill code
     * This assumes bill codes have discount percentage or amount
     */
    public function calculateDiscount($orderAmount)
    {
        if (!$this->isValidForDiscount()) {
            return 0;
        }

        // If bill code has discount_percentage field
        if (isset($this->discount_percentage) && $this->discount_percentage > 0) {
            return ($orderAmount * $this->discount_percentage) / 100;
        }

        // If bill code has discount_amount field
        if (isset($this->discount_amount) && $this->discount_amount > 0) {
            return min($this->discount_amount, $orderAmount);
        }

        return 0;
    }

    /**
     * Get discount percentage for display
     */
    public function getDiscountPercentage($orderAmount)
    {
        if (isset($this->discount_percentage)) {
            return $this->discount_percentage;
        }

        if (isset($this->discount_amount) && $orderAmount > 0) {
            return round(($this->discount_amount / $orderAmount) * 100, 2);
        }

        return 0;
    }
}
