<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Coupon extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'coupons';
    protected $primaryKey = 'id';
    
    protected $fillable = [
        'code',
        'type', // 'percentage' or 'fixed'
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'used_count',
        'start_date',
        'end_date',
        'status'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'status' => 'boolean'
    ];

    /**
     * Check if coupon is valid
     */
    public function isValid($orderAmount = 0)
    {
        // Check if coupon is active
        if (!$this->status) {
            return false;
        }

        // Check if coupon has not expired
        if ($this->end_date && Carbon::now()->gt($this->end_date)) {
            return false;
        }

        // Check if coupon has started
        if ($this->start_date && Carbon::now()->lt($this->start_date)) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        // Check minimum amount
        if ($this->minimum_amount && $orderAmount < $this->minimum_amount) {
            return false;
        }

        return true;
    }

    /**
     * Calculate discount amount
     */
    public function calculateDiscount($orderAmount)
    {
        if (!$this->isValid($orderAmount)) {
            return 0;
        }

        if ($this->type === 'percentage') {
            $discount = ($orderAmount * $this->value) / 100;
            
            // Apply maximum discount limit if set
            if ($this->maximum_discount && $discount > $this->maximum_discount) {
                $discount = $this->maximum_discount;
            }
            
            return $discount;
        } elseif ($this->type === 'fixed') {
            return min($this->value, $orderAmount);
        }

        return 0;
    }

    /**
     * Increment usage count
     */
    public function incrementUsage()
    {
        $this->increment('used_count');
    }

    /**
     * Get discount percentage for display
     */
    public function getDiscountPercentage($orderAmount)
    {
        if ($this->type === 'percentage') {
            return $this->value;
        } elseif ($this->type === 'fixed') {
            return round(($this->value / $orderAmount) * 100, 2);
        }

        return 0;
    }
}
