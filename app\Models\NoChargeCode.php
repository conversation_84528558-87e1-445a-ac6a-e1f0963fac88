<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NoChargeCode extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'nochargecode';
    protected $primaryKey = 'NoChargeCd';
    protected $keyType = 'string';
    protected $guarded = [];

    /**
     * Check if no charge code is valid and active
     */
    public function isValid()
    {
        return $this->CurrInd == 1; // Assuming CurrInd is the status field
    }

    /**
     * Get status HTML for display
     */
    public function getStatusHtmlAttribute()
    {
        return $this->CurrInd == 1 ? 
            '<span style="cursor:pointer;" class="success change-nocharge-status" nocharge_id="'.$this->NoChargeCd.'" nocharge_status="0">Active</span>' : 
            '<span class="danger change-nocharge-status" nocharge_id="'.$this->NoChargeCd.'" nocharge_status="1" style="cursor:pointer;">Inactive</span>';
    }
}
