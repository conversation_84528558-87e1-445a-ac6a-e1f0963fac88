@extends('website.layout.master')
@push('css')
    <style>
        /*Responsiveness*/
        @media screen and (max-width: 600px) {
            .cart_sidebar .product_cart_box .cart_sidebar_info h5 {font-size: 16px;}
            .cart_sidebar .product_cart_box .cart_sidebar_info h6 {font-size: 18px;}
            .cart_sidebar .product_cart_box .cart_sidebar_info span {font-size: 14px;}
            .cart_sidebar .product_cart_box .cart_sidebar_info p {font-size: 14px;}
        }
    </style>
@endpush

@section('content')


    <section class="shopping_cart_section pt-5 mt-5">
        <div class="container mt-lg-5 mt-3">
            <div class="row px-3">
                <div class="col-lg-12">
                    <nav>
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="#">Home</a></li>
                            <li class="breadcrumb-item"><a href="#">Order Clubs</a></li>
                            <li class="breadcrumb-item active">Cart</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-md-12">
                    <div class="shopping_box mt-3 mb-4">
                        <h6 class="fs-28 fw-600 black">Shopping Cart <span class="shopping_box_count black fs-18 fw-400">({{ sprintf('%02d', count($clubs)) }})</span></h6>
                    </div>
                </div>
                <div class="col-lg-7 col-md-6 col-sm-12 mt-4">
                    <div class="cart_sidebar">
                        @foreach($clubs as $club)
                            <div class="product_cart_box">
                                <div class="cart_sidebar_img">
                                    @if (isset($club->model->image) && $club->model->image != null)
                                        <img class="img-fluid" src="{{asset('website')}}/{{$club->model->image??'assets/images/custom-build-image.png'}}" alt="" @if(isset($club->HndSide) && $club->HndSide == "R") style="transform: scaleX(-1);" @endif>
                                    @else
                                        <img class="img-fluid" src="{{asset('website')}}/{{$club->category->image??'assets/images/custom-build-image.png'}}" alt="" @if(isset($club->HndSide) && $club->HndSide == "R") style="transform: scaleX(-1);" @endif>
                                    @endif
                                </div>
                                <div class="cart_sidebar_info">
                                    <h5>{{$club->model->HeadDsgnCd .' - '. $club->model->HeadDsgnDsc}}</h5>
                                    <h6>${{number_format($club->EachAmt ?? 0, 2)}}</h6>
                                    <div class="color_type">
                                        <p>Color:</p>
                                        <span style="background-color: {{$club->Color??'var(--green)'}}; width: 25px; height: 25px; border: 1px solid grey"></span>
                                    </div>
                                    <div class="color_type">
                                        <p>Quantity:</p>
                                        <span>{{ sprintf('%02d', $club->Qty ?? 0) }}</span>
                                    </div>
                                    @if(isset($club->ClbNum) && $club->ClbNum != null)
                                        <div class="color_type">
                                            <p>Club Number:</p>
                                            <span>{{ $club->ClbNum ??''}}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="col-lg-5 col-md-6 col-sm-12 mt-4">
                    <div class="summary">
                        <div class="summary_box">
                            <div class="detail_content">
                                <h6>Order Summary</h6>
                                <div class="detail_value sub_total">
                                    <p>Sub Total</p>
                                    <p>${{ number_format($order->TtlAmt ?? 0, 2) }}</p>
                                </div>
                                <div class="detail_value discount">
                                    <p>Discount</p>
                                    <p>{{ isset($coupon) ? $coupon['discount_percentage'] : 0 }}%</p>
                                </div>
                                <div class="detail_value shipping">
                                    <p>Shipping</p>
                                    <p class="free">Free</p>
                                </div>
                                <div class="detail_value coupon_applied">
                                    <p>{{ isset($coupon) ? $coupon['type'] . ' Applied' : 'Coupon Applied' }}</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['discount_amount'], 2) : '0.00' }}</p>
                                </div>
                            </div>
                            <div class="total_content">
                                <div class="total_value">
                                    <p>TOTAL</p>
                                    <p>${{ number_format($order->TtlAmt ?? 0, 2) }}</p>
                                </div>
                                <div class="coupon_field">
                                    <input type="text" placeholder="Coupon Code" name="coupon_code" id="coupon_code"
                                           value="{{ isset($coupon) ? $coupon['code'] : '' }}">
                                    <i class="fa-regular fa-tag"></i>
                                    @if(isset($coupon))
                                        <button type="button" id="remove_coupon_btn" class="btn btn-sm btn-danger ms-2">
                                            Remove
                                        </button>
                                    @endif
                                </div>
                                <div class="view_more_btn light">
                                    <button type="button" id="proceed_checkout_btn" class="btn btn-primary btn-sort">
                                        Proceed to Checkout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



@endsection
@push('js')
<script>
$(document).ready(function() {
    $('#proceed_checkout_btn').on('click', function() {
        var couponCode = $('#coupon_code').val().trim();
        if (couponCode) {
            applyCoupon(couponCode);
        } else {
            window.location.href = "{{ url('checkout') }}";
        }
    });
    $('#remove_coupon_btn').on('click', function() {
        removeCoupon();
    });
    function applyCoupon(couponCode) {
        $('#proceed_checkout_btn').prop('disabled', true).text('Applying Coupon...');
        $.ajax({
            url: "{{ route('apply-coupon') }}",
            type: 'POST',
            data: {
                coupon_code: couponCode,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    updateDiscountUI(response.discount, response.discount_amount, response.new_total);
                    showMessage('success', 'Coupon applied successfully!');
                    setTimeout(function() {
                        window.location.href = "{{ url('checkout') }}";
                    }, 1500);
                } else {
                    showMessage('error', response.message || 'Invalid coupon code');
                    resetButton();
                }
            },
            error: function(xhr, status, error) {
                showMessage('error', 'Error applying coupon. Please try again.');
                resetButton();
            }
        });
    }

    function updateDiscountUI(discountPercent, discountAmount, newTotal) {
        // Update discount percentage
        $('.detail_value.discount p:last-child').text(discountPercent + '%');

        // Update coupon applied amount
        $('.detail_value.coupon_applied p:last-child').text('$' + discountAmount.toFixed(2));

        // Update total amount
        $('.total_value p:last-child').text('$' + newTotal.toFixed(2));
    }

    function showMessage(type, message) {
        // Create and show a toast/alert message
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';

        // Insert the alert at the top of the container
        $('.container').prepend(alertHtml);

        // Auto-hide after 3 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    function resetButton() {
        $('#proceed_checkout_btn').prop('disabled', false).text('Proceed to Checkout');
    }

    function removeCoupon() {
        $.ajax({
            url: "{{ route('remove-coupon') }}",
            type: 'POST',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    // Reload the page to reflect changes
                    location.reload();
                } else {
                    showMessage('error', response.message || 'Error removing coupon');
                }
            },
            error: function(xhr, status, error) {
                showMessage('error', 'Error removing coupon. Please try again.');
            }
        });
    }
});
</script>
@endpush
